allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

rootProject.buildDir = "../build"
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
    project.evaluationDependsOn(":app")

    // Configure Java toolchain to use Java 8 for compilation
    tasks.withType(JavaCompile).configureEach {
        options.release = 8
    }
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
