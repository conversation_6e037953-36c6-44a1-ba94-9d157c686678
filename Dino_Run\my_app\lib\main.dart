import 'package:flame/components.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flame/game.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.landscapeRight,
  ]);

  // Hide system UI for fullscreen
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Dino Run',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
        useMaterial3: true,
      ),
      home: const MyHomePage(),
    );
  }
}

class MyHomePage extends StatefulWidget {
  const MyHomePage({super.key});

  @override
  State<MyHomePage> createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  late FlameGame game;

  @override
  void initState() {
    super.initState();
    game = FlameGame();

    //_loadDinoSprite();
  }

  Future<void> _loadDinoSprite() async {
    final dinoSprite = await Sprite.load('DinoSprites_tard.gif');

    final dinoComponent = SpriteComponent(sprite: dinoSprite)
      ..size = Vector2(100, 100)
      ..position = Vector2(100, 100);

    game.add(dinoComponent);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          backgroundColor: Theme.of(context).colorScheme.inversePrimary,
          title: const Text('Dino Run'),
        ),
        //body: Center(
          child: GameWidget(game: game),
        ));
  }
}
